// Import Firebase modules
import { initializeApp } from "firebase/app";
import { getDatabase, ref, get } from "firebase/database";

// Your Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4I2xUx2puBoiDHvnF3lDsYKjZNSqiq7E",
  authDomain: "portfolio-cd2a1.firebaseapp.com",
  databaseURL: "https://portfolio-cd2a1-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "portfolio-cd2a1",
  storageBucket: "portfolio-cd2a1.appspot.com",
  messagingSenderId: "696467671655",
  appId: "1:696467671655:web:4ee450fc9e3bc43dcf62c2",
  measurementId: "G-GDD2XGP9HN"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const database = getDatabase(app);

// Fetch data once
get(ref(database, '/'))
  .then((snapshot) => {
    if (snapshot.exists()) {
      console.log("Data:", snapshot.val());
    } else {
      console.log("No data found.");
    }
  })
  .catch((error) => {
    console.error("Error:", error);
  });

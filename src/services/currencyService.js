/**
 * Currency conversion service
 */

// Default exchange rate (USD to INR)
// In a production app, this would be fetched from a real-time API
const DEFAULT_USD_TO_INR_RATE = 85.72;

/**
 * Currency configuration
 */
export const CURRENCIES = {
  INR: {
    code: 'INR',
    symbol: '₹',
    name: 'Indian Rupee'
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar'
  }
};

/**
 * Get current exchange rate (USD to INR)
 * In a real application, this would fetch from a live API
 * @returns {Promise<number>} Exchange rate
 */
export const getExchangeRate = async () => {
  try {
    // In a real app, you would fetch from an API like:
    // const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
    // const data = await response.json();
    // return data.rates.INR;
    
    // For now, return a static rate
    return DEFAULT_USD_TO_INR_RATE;
  } catch (error) {
    console.error('Error fetching exchange rate:', error);
    return DEFAULT_USD_TO_INR_RATE;
  }
};

/**
 * Convert amount from one currency to another
 * @param {number} amount - Amount to convert
 * @param {string} fromCurrency - Source currency code ('INR' or 'USD')
 * @param {string} toCurrency - Target currency code ('INR' or 'USD')
 * @param {number} exchangeRate - USD to INR exchange rate
 * @returns {number} Converted amount
 */
export const convertCurrency = (amount, fromCurrency, toCurrency, exchangeRate = DEFAULT_USD_TO_INR_RATE) => {
  if (fromCurrency === toCurrency) {
    return amount;
  }

  if (fromCurrency === 'USD' && toCurrency === 'INR') {
    return amount * exchangeRate;
  } else if (fromCurrency === 'INR' && toCurrency === 'USD') {
    return amount / exchangeRate;
  }

  return amount;
};

/**
 * Format currency amount with appropriate symbol and locale
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code ('INR' or 'USD')
 * @param {boolean} showDecimals - Whether to show decimal places
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'INR', showDecimals = true) => {
  const currencyConfig = CURRENCIES[currency];
  if (!currencyConfig) {
    throw new Error(`Unsupported currency: ${currency}`);
  }

  const options = {
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0
  };

  // Use Indian locale for INR, US locale for USD
  const locale = currency === 'INR' ? 'en-IN' : 'en-US';
  
  const formattedAmount = new Intl.NumberFormat(locale, options).format(Math.abs(amount));
  const sign = amount < 0 ? '-' : '';
  
  return `${sign}${currencyConfig.symbol}${formattedAmount}`;
};

/**
 * Format percentage with appropriate sign and color indication
 * @param {number} percentage - Percentage value
 * @param {boolean} showSign - Whether to show + sign for positive values
 * @returns {Object} - {value: string, isPositive: boolean, isNegative: boolean}
 */
export const formatPercentage = (percentage, showSign = true) => {
  const isPositive = percentage > 0;
  const isNegative = percentage < 0;
  const sign = showSign && isPositive ? '+' : '';
  
  const formattedValue = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(Math.abs(percentage));

  return {
    value: `${sign}${isNegative ? '-' : ''}${formattedValue}%`,
    isPositive,
    isNegative,
    isNeutral: percentage === 0
  };
};

/**
 * Get currency display information
 * @param {string} currency - Currency code
 * @returns {Object} Currency display information
 */
export const getCurrencyInfo = (currency) => {
  return CURRENCIES[currency] || CURRENCIES.INR;
};

/**
 * Toggle between INR and USD
 * @param {string} currentCurrency - Current currency code
 * @returns {string} Toggled currency code
 */
export const toggleCurrency = (currentCurrency) => {
  return currentCurrency === 'INR' ? 'USD' : 'INR';
};

/**
 * Validate currency code
 * @param {string} currency - Currency code to validate
 * @returns {boolean} Whether the currency is supported
 */
export const isValidCurrency = (currency) => {
  return Object.keys(CURRENCIES).includes(currency);
};

/**
 * Get all supported currencies
 * @returns {Array} Array of currency objects
 */
export const getSupportedCurrencies = () => {
  return Object.values(CURRENCIES);
};

/**
 * Simple test file to verify portfolio calculations
 * Run this with: node src/utils/test-calculations.js
 */

import { getPortfolioSummary, formatCurrency } from './portfolioCalculations.js';

// Sample test data (simplified version of example.json)
const testData = {
  accounts: {
    bank_accounts: {
      Axis: 218562,
      SBI: 13517
    },
    brokers: {
      Zerodha: {
        stocks_inr: [
          {
            symbol: "BAJFINANCE",
            invested: 28005,
            current: 28099,
            shares: 11
          }
        ],
        funds_inr: 10118
      },
      INDmoney: {
        stocks_usd: [
          {
            symbol: "TSLA",
            invested: 114.66,
            current: 100.20,
            shares: 0.3305
          }
        ]
      }
    }
  }
};

console.log('Testing Portfolio Calculations...\n');

// Test INR calculations
const summaryINR = getPortfolioSummary(testData, 'INR');
console.log('INR Summary:');
console.log('Net Worth:', formatCurrency(summaryINR.netWorth, 'INR'));
console.log('Total Invested:', formatCurrency(summaryINR.totalInvested, 'INR'));
console.log('Current Value:', formatCurrency(summaryINR.currentValue, 'INR'));
console.log('Unrealized P&L:', formatCurrency(summaryINR.unrealizedPL, 'INR'));
console.log('Percentage Change:', summaryINR.percentageChange.toFixed(2) + '%');
console.log('');

// Test USD calculations
const summaryUSD = getPortfolioSummary(testData, 'USD');
console.log('USD Summary:');
console.log('Net Worth:', formatCurrency(summaryUSD.netWorth, 'USD'));
console.log('Total Invested:', formatCurrency(summaryUSD.totalInvested, 'USD'));
console.log('Current Value:', formatCurrency(summaryUSD.currentValue, 'USD'));
console.log('Unrealized P&L:', formatCurrency(summaryUSD.unrealizedPL, 'USD'));
console.log('Percentage Change:', summaryUSD.percentageChange.toFixed(2) + '%');

console.log('\nTest completed successfully!');

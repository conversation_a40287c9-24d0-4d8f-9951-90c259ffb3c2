/**
 * Risk and Performance Metrics Calculations
 */

/**
 * Calculate XIRR (Extended Internal Rate of Return) using Newton-Raphson method
 * @param {Array} cashFlows - Array of {date: Date, amount: number} objects
 * @param {number} guess - Initial guess for IRR (default: 0.1)
 * @returns {number} XIRR as decimal (e.g., 0.15 for 15%)
 */
export const calculateXIRR = (cashFlows, guess = 0.1) => {
  if (!cashFlows || cashFlows.length < 2) return 0;

  // Sort cash flows by date
  const sortedFlows = [...cashFlows].sort((a, b) => new Date(a.date) - new Date(b.date));
  const firstDate = new Date(sortedFlows[0].date);
  
  // Convert dates to years from first date
  const flows = sortedFlows.map(flow => ({
    amount: flow.amount,
    years: (new Date(flow.date) - firstDate) / (365.25 * 24 * 60 * 60 * 1000)
  }));

  let rate = guess;
  const maxIterations = 100;
  const tolerance = 1e-6;

  for (let i = 0; i < maxIterations; i++) {
    let npv = 0;
    let dnpv = 0;

    flows.forEach(flow => {
      const factor = Math.pow(1 + rate, flow.years);
      npv += flow.amount / factor;
      dnpv -= flow.amount * flow.years / (factor * (1 + rate));
    });

    if (Math.abs(npv) < tolerance) break;
    if (Math.abs(dnpv) < tolerance) break;

    rate = rate - npv / dnpv;
  }

  return isNaN(rate) || !isFinite(rate) ? 0 : rate;
};

/**
 * Generate mock historical returns for portfolio
 * @param {Object} portfolioData - Portfolio data
 * @param {number} months - Number of months of history
 * @returns {Array} Array of monthly returns
 */
export const generateMockReturns = (portfolioData, months = 24) => {
  const returns = [];
  const baseReturn = 0.01; // 1% monthly base return
  
  for (let i = 0; i < months; i++) {
    // Generate realistic returns with some volatility
    const randomFactor = (Math.random() - 0.5) * 0.1; // ±5% volatility
    const seasonalFactor = Math.sin((i / 12) * 2 * Math.PI) * 0.02; // Seasonal variation
    const monthlyReturn = baseReturn + randomFactor + seasonalFactor;
    returns.push(monthlyReturn);
  }
  
  return returns;
};

/**
 * Generate mock benchmark returns (e.g., Nifty 50, S&P 500)
 * @param {number} months - Number of months of history
 * @returns {Array} Array of monthly benchmark returns
 */
export const generateMockBenchmarkReturns = (months = 24) => {
  const returns = [];
  const baseReturn = 0.008; // 0.8% monthly base return for benchmark
  
  for (let i = 0; i < months; i++) {
    const randomFactor = (Math.random() - 0.5) * 0.08; // ±4% volatility (lower than portfolio)
    const seasonalFactor = Math.sin((i / 12) * 2 * Math.PI) * 0.015;
    const monthlyReturn = baseReturn + randomFactor + seasonalFactor;
    returns.push(monthlyReturn);
  }
  
  return returns;
};

/**
 * Calculate portfolio volatility (standard deviation of returns)
 * @param {Array} returns - Array of periodic returns
 * @returns {number} Annualized volatility
 */
export const calculateVolatility = (returns) => {
  if (!returns || returns.length < 2) return 0;

  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1);
  const monthlyStdDev = Math.sqrt(variance);
  
  // Annualize (assuming monthly returns)
  return monthlyStdDev * Math.sqrt(12);
};

/**
 * Calculate beta (portfolio sensitivity to benchmark)
 * @param {Array} portfolioReturns - Portfolio returns
 * @param {Array} benchmarkReturns - Benchmark returns
 * @returns {number} Beta coefficient
 */
export const calculateBeta = (portfolioReturns, benchmarkReturns) => {
  if (!portfolioReturns || !benchmarkReturns || portfolioReturns.length !== benchmarkReturns.length) {
    return 1; // Default beta
  }

  const n = portfolioReturns.length;
  if (n < 2) return 1;

  const portfolioMean = portfolioReturns.reduce((sum, r) => sum + r, 0) / n;
  const benchmarkMean = benchmarkReturns.reduce((sum, r) => sum + r, 0) / n;

  let covariance = 0;
  let benchmarkVariance = 0;

  for (let i = 0; i < n; i++) {
    const portfolioDiff = portfolioReturns[i] - portfolioMean;
    const benchmarkDiff = benchmarkReturns[i] - benchmarkMean;
    
    covariance += portfolioDiff * benchmarkDiff;
    benchmarkVariance += benchmarkDiff * benchmarkDiff;
  }

  covariance /= (n - 1);
  benchmarkVariance /= (n - 1);

  return benchmarkVariance === 0 ? 1 : covariance / benchmarkVariance;
};

/**
 * Calculate Sharpe ratio (risk-adjusted return)
 * @param {Array} returns - Portfolio returns
 * @param {number} riskFreeRate - Risk-free rate (annual)
 * @returns {number} Sharpe ratio
 */
export const calculateSharpeRatio = (returns, riskFreeRate = 0.06) => {
  if (!returns || returns.length < 2) return 0;

  const annualReturn = returns.reduce((sum, r) => sum + r, 0) * 12 / returns.length;
  const volatility = calculateVolatility(returns);
  
  if (volatility === 0) return 0;
  
  return (annualReturn - riskFreeRate) / volatility;
};

/**
 * Calculate maximum drawdown
 * @param {Array} returns - Portfolio returns
 * @returns {number} Maximum drawdown as decimal
 */
export const calculateMaxDrawdown = (returns) => {
  if (!returns || returns.length < 2) return 0;

  let peak = 1;
  let maxDrawdown = 0;
  let currentValue = 1;

  returns.forEach(returnRate => {
    currentValue *= (1 + returnRate);
    if (currentValue > peak) {
      peak = currentValue;
    }
    const drawdown = (peak - currentValue) / peak;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  });

  return maxDrawdown;
};

/**
 * Calculate Value at Risk (VaR) at 95% confidence level
 * @param {Array} returns - Portfolio returns
 * @param {number} confidence - Confidence level (default: 0.95)
 * @returns {number} VaR as decimal
 */
export const calculateVaR = (returns, confidence = 0.95) => {
  if (!returns || returns.length < 2) return 0;

  const sortedReturns = [...returns].sort((a, b) => a - b);
  const index = Math.floor((1 - confidence) * sortedReturns.length);
  
  return Math.abs(sortedReturns[index] || 0);
};

/**
 * Calculate all risk metrics for a portfolio
 * @param {Object} portfolioData - Portfolio data
 * @param {string} currency - Currency for calculations
 * @returns {Object} Complete risk metrics
 */
export const calculateRiskMetrics = (portfolioData, currency = 'INR') => {
  // Generate mock data for demonstration
  const portfolioReturns = generateMockReturns(portfolioData, 24);
  const benchmarkReturns = generateMockBenchmarkReturns(24);
  
  // Generate mock cash flows for XIRR calculation
  const today = new Date();
  const cashFlows = [
    { date: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000), amount: -100000 }, // Initial investment
    { date: new Date(today.getTime() - 180 * 24 * 60 * 60 * 1000), amount: -50000 },  // Additional investment
    { date: today, amount: 180000 } // Current value
  ];

  const xirr = calculateXIRR(cashFlows);
  const volatility = calculateVolatility(portfolioReturns);
  const beta = calculateBeta(portfolioReturns, benchmarkReturns);
  const sharpeRatio = calculateSharpeRatio(portfolioReturns);
  const maxDrawdown = calculateMaxDrawdown(portfolioReturns);
  const var95 = calculateVaR(portfolioReturns);

  const annualReturn = portfolioReturns.reduce((sum, r) => sum + r, 0) * 12 / portfolioReturns.length;
  const benchmarkReturn = benchmarkReturns.reduce((sum, r) => sum + r, 0) * 12 / benchmarkReturns.length;

  return {
    xirr,
    annualReturn,
    volatility,
    beta,
    sharpeRatio,
    maxDrawdown,
    var95,
    benchmarkReturn,
    portfolioReturns,
    benchmarkReturns,
    currency
  };
};

/**
 * Portfolio calculation utilities
 */

// Exchange rate (USD to INR) - In a real app, this would be fetched from an API
const USD_TO_INR_RATE = 83.5; // Approximate rate

/**
 * Calculate total invested amount across all accounts
 * @param {Object} portfolioData - Portfolio data object
 * @returns {Object} - {inr: number, usd: number}
 */
export const calculateTotalInvested = (portfolioData) => {
  if (!portfolioData?.accounts) return { inr: 0, usd: 0 };

  let totalInvestedINR = 0;
  let totalInvestedUSD = 0;

  const { bank_accounts, brokers } = portfolioData.accounts;

  // Add bank accounts (all in INR)
  if (bank_accounts) {
    Object.values(bank_accounts).forEach(amount => {
      totalInvestedINR += amount;
    });
  }

  // Process brokers
  if (brokers) {
    Object.values(brokers).forEach(broker => {
      // INR stocks
      if (broker.stocks_inr) {
        broker.stocks_inr.forEach(stock => {
          totalInvestedINR += stock.invested;
        });
      }

      // USD stocks
      if (broker.stocks_usd) {
        broker.stocks_usd.forEach(stock => {
          totalInvestedUSD += stock.invested;
        });
      }

      // INR cryptos
      if (broker.cryptos_inr) {
        broker.cryptos_inr.forEach(crypto => {
          totalInvestedINR += crypto.invested;
        });
      }

      // USD cryptos
      if (broker.cryptos_usd) {
        broker.cryptos_usd.forEach(crypto => {
          totalInvestedUSD += crypto.invested;
        });
      }

      // Mutual funds (INR)
      if (broker.mutual_funds) {
        broker.mutual_funds.forEach(fund => {
          totalInvestedINR += fund.invested;
        });
      }

      // Available funds (INR)
      if (broker.funds_inr) {
        totalInvestedINR += broker.funds_inr;
      }
    });
  }

  return {
    inr: totalInvestedINR,
    usd: totalInvestedUSD
  };
};

/**
 * Calculate current total value across all accounts
 * @param {Object} portfolioData - Portfolio data object
 * @returns {Object} - {inr: number, usd: number}
 */
export const calculateCurrentValue = (portfolioData) => {
  if (!portfolioData?.accounts) return { inr: 0, usd: 0 };

  let currentValueINR = 0;
  let currentValueUSD = 0;

  const { bank_accounts, brokers } = portfolioData.accounts;

  // Add bank accounts (all in INR)
  if (bank_accounts) {
    Object.values(bank_accounts).forEach(amount => {
      currentValueINR += amount;
    });
  }

  // Process brokers
  if (brokers) {
    Object.values(brokers).forEach(broker => {
      // INR stocks
      if (broker.stocks_inr) {
        broker.stocks_inr.forEach(stock => {
          currentValueINR += stock.current;
        });
      }

      // USD stocks
      if (broker.stocks_usd) {
        broker.stocks_usd.forEach(stock => {
          currentValueUSD += stock.current;
        });
      }

      // INR cryptos
      if (broker.cryptos_inr) {
        broker.cryptos_inr.forEach(crypto => {
          currentValueINR += crypto.current;
        });
      }

      // USD cryptos
      if (broker.cryptos_usd) {
        broker.cryptos_usd.forEach(crypto => {
          currentValueUSD += crypto.current;
        });
      }

      // Mutual funds (INR)
      if (broker.mutual_funds) {
        broker.mutual_funds.forEach(fund => {
          currentValueINR += fund.current;
        });
      }

      // Available funds (INR)
      if (broker.funds_inr) {
        currentValueINR += broker.funds_inr;
      }
    });
  }

  return {
    inr: currentValueINR,
    usd: currentValueUSD
  };
};

/**
 * Calculate unrealized P&L
 * @param {Object} portfolioData - Portfolio data object
 * @returns {Object} - {inr: number, usd: number}
 */
export const calculateUnrealizedPL = (portfolioData) => {
  const totalInvested = calculateTotalInvested(portfolioData);
  const currentValue = calculateCurrentValue(portfolioData);

  return {
    inr: currentValue.inr - totalInvested.inr,
    usd: currentValue.usd - totalInvested.usd
  };
};

/**
 * Calculate percentage change
 * @param {Object} portfolioData - Portfolio data object
 * @returns {Object} - {inr: number, usd: number}
 */
export const calculatePercentageChange = (portfolioData) => {
  const totalInvested = calculateTotalInvested(portfolioData);
  const unrealizedPL = calculateUnrealizedPL(portfolioData);

  const inrPercentage = totalInvested.inr > 0 ? (unrealizedPL.inr / totalInvested.inr) * 100 : 0;
  const usdPercentage = totalInvested.usd > 0 ? (unrealizedPL.usd / totalInvested.usd) * 100 : 0;

  return {
    inr: inrPercentage,
    usd: usdPercentage
  };
};

/**
 * Calculate net worth in both currencies
 * @param {Object} portfolioData - Portfolio data object
 * @returns {Object} - {inr: number, usd: number}
 */
export const calculateNetWorth = (portfolioData) => {
  const currentValue = calculateCurrentValue(portfolioData);

  return {
    inr: currentValue.inr + (currentValue.usd * USD_TO_INR_RATE),
    usd: (currentValue.inr / USD_TO_INR_RATE) + currentValue.usd
  };
};

/**
 * Convert amount between currencies
 * @param {number} amount - Amount to convert
 * @param {string} fromCurrency - 'INR' or 'USD'
 * @param {string} toCurrency - 'INR' or 'USD'
 * @returns {number} - Converted amount
 */
export const convertCurrency = (amount, fromCurrency, toCurrency) => {
  if (fromCurrency === toCurrency) return amount;

  if (fromCurrency === 'USD' && toCurrency === 'INR') {
    return amount * USD_TO_INR_RATE;
  } else if (fromCurrency === 'INR' && toCurrency === 'USD') {
    return amount / USD_TO_INR_RATE;
  }

  return amount;
};

/**
 * Format currency value with appropriate symbol
 * @param {number} amount - Amount to format
 * @param {string} currency - 'INR' or 'USD'
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (amount, currency = 'INR') => {
  const symbol = currency === 'INR' ? '₹' : '$';
  const formattedAmount = new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(Math.abs(amount));

  const sign = amount < 0 ? '-' : '';
  return `${sign}${symbol}${formattedAmount}`;
};

/**
 * Get portfolio summary with all key metrics
 * @param {Object} portfolioData - Portfolio data object
 * @param {string} currency - 'INR' or 'USD'
 * @returns {Object} - Complete portfolio summary
 */
export const getPortfolioSummary = (portfolioData, currency = 'INR') => {
  const totalInvested = calculateTotalInvested(portfolioData);
  const currentValue = calculateCurrentValue(portfolioData);
  const netWorth = calculateNetWorth(portfolioData);

  // Convert all values to the requested currency
  const convertedTotalInvested = currency === 'INR'
    ? totalInvested.inr + convertCurrency(totalInvested.usd, 'USD', 'INR')
    : convertCurrency(totalInvested.inr, 'INR', 'USD') + totalInvested.usd;

  const convertedCurrentValue = currency === 'INR'
    ? currentValue.inr + convertCurrency(currentValue.usd, 'USD', 'INR')
    : convertCurrency(currentValue.inr, 'INR', 'USD') + currentValue.usd;

  const convertedUnrealizedPL = convertedCurrentValue - convertedTotalInvested;
  const convertedPercentageChange = convertedTotalInvested > 0
    ? (convertedUnrealizedPL / convertedTotalInvested) * 100
    : 0;

  return {
    netWorth: currency === 'INR' ? netWorth.inr : netWorth.usd,
    totalInvested: convertedTotalInvested,
    currentValue: convertedCurrentValue,
    unrealizedPL: convertedUnrealizedPL,
    percentageChange: convertedPercentageChange,
    currency
  };
};

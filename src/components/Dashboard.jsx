import React, { useState, useEffect } from 'react';
import { fetchPortfolioDataWithFallback } from '../services/firebaseService';
import { toggleCurrency } from '../services/currencyService';
import PortfolioSummary from './PortfolioSummary';
import PNL<PERSON>hart from './PNLChart';
import AssetTable from './AssetTable';
import DiversificationAnalysis from './DiversificationAnalysis';
import AccountDetails from './AccountDetails';
import RiskMetrics from './RiskMetrics';
import './Dashboard.css';

const Dashboard = () => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [currency, setCurrency] = useState('INR');
  const [activeView, setActiveView] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch portfolio data on component mount
  useEffect(() => {
    const loadPortfolioData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchPortfolioDataWithFallback();
        setPortfolioData(data);
      } catch (err) {
        setError('Failed to load portfolio data');
        console.error('Error loading portfolio data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPortfolioData();
  }, []);

  const handleCurrencyToggle = () => {
    setCurrency(prevCurrency => toggleCurrency(prevCurrency));
  };

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'performance', label: 'Performance', icon: '📈' },
    { id: 'assets', label: 'Assets', icon: '📋' },
    { id: 'diversification', label: 'Diversification', icon: '🥧' },
    { id: 'risk', label: 'Risk Metrics', icon: '⚡' },
    { id: 'accounts', label: 'Accounts', icon: '🏦' }
  ];

  const renderActiveView = () => {
    if (loading) {
      return (
        <div className="dashboard-loading">
          <div className="loading-spinner"></div>
          <p>Loading portfolio data...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="dashboard-error">
          <div className="error-message">
            <h3>Error</h3>
            <p>{error}</p>
            <button onClick={() => window.location.reload()}>Retry</button>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'overview':
        return <PortfolioSummary portfolioData={portfolioData} currency={currency} />;
      case 'performance':
        return <PNLChart portfolioData={portfolioData} currency={currency} />;
      case 'assets':
        return <AssetTable portfolioData={portfolioData} currency={currency} />;
      case 'diversification':
        return <DiversificationAnalysis portfolioData={portfolioData} currency={currency} />;
      case 'risk':
        return <RiskMetrics portfolioData={portfolioData} currency={currency} />;
      case 'accounts':
        return <AccountDetails portfolioData={portfolioData} currency={currency} />;
      default:
        return <PortfolioSummary portfolioData={portfolioData} currency={currency} />;
    }
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Net Worth Dashboard</h1>
          <div className="header-controls">
            <button
              className="currency-toggle"
              onClick={handleCurrencyToggle}
              title={`Switch to ${currency === 'INR' ? 'USD' : 'INR'}`}
            >
              {currency === 'INR' ? '₹' : '$'} {currency}
            </button>
          </div>
        </div>

        <nav className="dashboard-nav">
          {navigationItems.map(item => (
            <button
              key={item.id}
              className={`nav-item ${activeView === item.id ? 'active' : ''}`}
              onClick={() => setActiveView(item.id)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="dashboard-content">
        {renderActiveView()}
      </div>
    </div>
  );
};

export default Dashboard;

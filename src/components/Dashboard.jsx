import React, { useState, useEffect } from 'react';
import { fetchPortfolioDataWithFallback } from '../services/firebaseService';
import { toggleCurrency } from '../services/currencyService';
import PortfolioSummary from './PortfolioSummary';
import PNL<PERSON>hart from './PNLChart';
import AssetTable from './AssetTable';
import DiversificationAnalysis from './DiversificationAnalysis';
import AccountDetails from './AccountDetails';
import RiskMetrics from './RiskMetrics';
import Toast from './Toast';
import './Dashboard.css';

const Dashboard = () => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [currency, setCurrency] = useState('INR');
  const [activeView, setActiveView] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [toast, setToast] = useState({ isVisible: false, message: '', type: 'success' });

  // Function to load portfolio data
  const loadPortfolioData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setError(null);
      } else {
        setLoading(true);
        setError(null);
      }

      const data = await fetchPortfolioDataWithFallback();
      setPortfolioData(data);
      setLastUpdated(new Date());

      // Show success toast for refresh
      if (isRefresh) {
        setToast({
          isVisible: true,
          message: 'Portfolio data refreshed successfully!',
          type: 'success'
        });
      }
    } catch (err) {
      setError('Failed to load portfolio data');
      console.error('Error loading portfolio data:', err);

      // Show error toast for refresh
      if (isRefresh) {
        setToast({
          isVisible: true,
          message: 'Failed to refresh data. Please try again.',
          type: 'error'
        });
      }
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  // Fetch portfolio data on component mount
  useEffect(() => {
    loadPortfolioData();
  }, []);

  // Refresh function
  const handleRefresh = () => {
    loadPortfolioData(true);
  };

  // Toast close handler
  const handleToastClose = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleCurrencyToggle = () => {
    setCurrency(prevCurrency => toggleCurrency(prevCurrency));
  };

  const formatLastUpdated = () => {
    if (!lastUpdated) return '';
    const now = new Date();
    const diffMs = now - lastUpdated;
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return lastUpdated.toLocaleDateString();
  };

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'performance', label: 'Performance', icon: '📈' },
    { id: 'assets', label: 'Assets', icon: '📋' },
    { id: 'diversification', label: 'Diversification', icon: '🥧' },
    { id: 'risk', label: 'Risk Metrics', icon: '⚡' },
    { id: 'accounts', label: 'Accounts', icon: '🏦' }
  ];

  const renderActiveView = () => {
    if (loading) {
      return (
        <div className="dashboard-loading">
          <div className="loading-spinner"></div>
          <p>Loading portfolio data...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="dashboard-error">
          <div className="error-message">
            <h3>Error</h3>
            <p>{error}</p>
            <button onClick={() => window.location.reload()}>Retry</button>
          </div>
        </div>
      );
    }

    switch (activeView) {
      case 'overview':
        return <PortfolioSummary portfolioData={portfolioData} currency={currency} />;
      case 'performance':
        return <PNLChart portfolioData={portfolioData} currency={currency} />;
      case 'assets':
        return <AssetTable portfolioData={portfolioData} currency={currency} />;
      case 'diversification':
        return <DiversificationAnalysis portfolioData={portfolioData} currency={currency} />;
      case 'risk':
        return <RiskMetrics portfolioData={portfolioData} currency={currency} />;
      case 'accounts':
        return <AccountDetails portfolioData={portfolioData} currency={currency} />;
      default:
        return <PortfolioSummary portfolioData={portfolioData} currency={currency} />;
    }
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="header-content">
          <h1>Net Worth Dashboard</h1>
          <div className="header-controls">
            <div className="last-updated">
              {lastUpdated && (
                <span className="update-time">
                  Updated {formatLastUpdated()}
                </span>
              )}
            </div>
            <button
              className={`refresh-btn ${refreshing ? 'refreshing' : ''}`}
              onClick={handleRefresh}
              disabled={refreshing}
              title="Refresh data from database"
            >
              <span className="refresh-icon">🔄</span>
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </button>
            <button
              className="currency-toggle"
              onClick={handleCurrencyToggle}
              title={`Switch to ${currency === 'INR' ? 'USD' : 'INR'}`}
            >
              {currency === 'INR' ? '₹' : '$'} {currency}
            </button>
          </div>
        </div>

        <nav className="dashboard-nav">
          {navigationItems.map(item => (
            <button
              key={item.id}
              className={`nav-item ${activeView === item.id ? 'active' : ''}`}
              onClick={() => setActiveView(item.id)}
            >
              <span className="nav-icon">{item.icon}</span>
              <span className="nav-label">{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="dashboard-content">
        {renderActiveView()}
      </div>

      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={handleToastClose}
        duration={3000}
      />
    </div>
  );
};

export default Dashboard;

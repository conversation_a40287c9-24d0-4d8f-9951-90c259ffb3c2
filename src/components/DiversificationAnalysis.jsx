import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import { Pie } from 'react-chartjs-2';
import { getAssetBreakdown, calculateTotalCash } from '../utils/portfolioCalculations';
import { formatCurrency, convertCurrency } from '../services/currencyService';
import './DiversificationAnalysis.css';

ChartJS.register(ArcElement, Tooltip, Legend);

const DiversificationAnalysis = ({ portfolioData, currency }) => {
  const [assetClassData, setAssetClassData] = useState(null);
  const [brokerData, setBrokerData] = useState(null);

  const generateAssetClassData = () => {
    const assets = getAssetBreakdown(portfolioData, currency);
    const cash = calculateTotalCash(portfolioData);

    // Convert cash to selected currency
    const totalCash = currency === 'INR'
      ? cash.inr + convertCurrency(cash.usd, 'USD', 'INR')
      : convertCurrency(cash.inr, 'INR', 'USD') + cash.usd;

    // Group by asset type
    const assetTypes = {};
    assets.forEach(asset => {
      if (!assetTypes[asset.type]) {
        assetTypes[asset.type] = 0;
      }
      assetTypes[asset.type] += asset.current;
    });

    // Add cash
    if (totalCash > 0) {
      assetTypes['Cash & Funds'] = totalCash;
    }

    const labels = Object.keys(assetTypes);
    const values = Object.values(assetTypes);
    const total = values.reduce((sum, value) => sum + value, 0);
    const percentages = values.map(value => ((value / total) * 100).toFixed(1));

    const colors = {
      'Stock': '#60a5fa',
      'Mutual Fund': '#34d399',
      'Cryptocurrency': '#a78bfa',
      'Cash & Funds': '#fbbf24'
    };

    const data = {
      labels: labels.map((label, index) => `${label} (${percentages[index]}%)`),
      datasets: [{
        data: values,
        backgroundColor: labels.map(label => colors[label] || '#64748b'),
        borderColor: labels.map(label => colors[label] || '#64748b'),
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverBorderColor: '#ffffff'
      }]
    };

    setAssetClassData(data);
  };

  const generateBrokerData = () => {
    const assets = getAssetBreakdown(portfolioData, currency);
    const cash = calculateTotalCash(portfolioData);

    // Convert cash to selected currency
    const totalCash = currency === 'INR'
      ? cash.inr + convertCurrency(cash.usd, 'USD', 'INR')
      : convertCurrency(cash.inr, 'INR', 'USD') + cash.usd;

    // Group by broker
    const brokers = {};
    assets.forEach(asset => {
      if (!brokers[asset.broker]) {
        brokers[asset.broker] = 0;
      }
      brokers[asset.broker] += asset.current;
    });

    // Add cash (distributed among bank accounts)
    if (totalCash > 0) {
      brokers['Bank Accounts'] = totalCash;
    }

    const labels = Object.keys(brokers);
    const values = Object.values(brokers);
    const total = values.reduce((sum, value) => sum + value, 0);
    const percentages = values.map(value => ((value / total) * 100).toFixed(1));

    const brokerColors = {
      'Zerodha': '#ff6b6b',
      'Groww': '#4ecdc4',
      'Dhan': '#45b7d1',
      'CoinDCX': '#f9ca24',
      'INDmoney': '#6c5ce7',
      'Revolut': '#a29bfe',
      'Bank Accounts': '#fbbf24'
    };

    const data = {
      labels: labels.map((label, index) => `${label} (${percentages[index]}%)`),
      datasets: [{
        data: values,
        backgroundColor: labels.map(label => brokerColors[label] || '#64748b'),
        borderColor: labels.map(label => brokerColors[label] || '#64748b'),
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverBorderColor: '#ffffff'
      }]
    };

    setBrokerData(data);
  };

  useEffect(() => {
    if (portfolioData) {
      generateAssetClassData();
      generateBrokerData();
    }
  }, [portfolioData, currency]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#f1f5f9',
          font: {
            size: 12,
            weight: 'bold'
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(30, 41, 59, 0.9)',
        titleColor: '#f1f5f9',
        bodyColor: '#cbd5e1',
        borderColor: 'rgba(148, 163, 184, 0.3)',
        borderWidth: 1,
        callbacks: {
          label: function (context) {
            const value = context.parsed;
            const total = context.dataset.data.reduce((sum, val) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label.split(' (')[0]}: ${formatCurrency(value, currency)} (${percentage}%)`;
          }
        }
      }
    },
    elements: {
      arc: {
        borderWidth: 2
      }
    }
  };

  if (!assetClassData || !brokerData) {
    return (
      <div className="diversification-analysis loading">
        <div className="loading-spinner"></div>
        <p>Loading diversification data...</p>
      </div>
    );
  }

  return (
    <div className="diversification-analysis">
      <div className="analysis-header">
        <h2>Portfolio Diversification</h2>
        <p>Breakdown of your investments across asset classes and platforms</p>
      </div>

      <div className="charts-grid">
        <div className="chart-card">
          <h3>Asset Class Distribution</h3>
          <div className="chart-container">
            <Pie data={assetClassData} options={chartOptions} />
          </div>
        </div>

        <div className="chart-card">
          <h3>Platform Distribution</h3>
          <div className="chart-container">
            <Pie data={brokerData} options={chartOptions} />
          </div>
        </div>
      </div>

      <div className="diversification-insights">
        <h3>Diversification Insights</h3>
        <div className="insights-grid">
          <div className="insight-card">
            <div className="insight-icon">📊</div>
            <div className="insight-content">
              <h4>Asset Classes</h4>
              <p>{Object.keys(assetClassData.datasets[0].data).length} different asset types in your portfolio</p>
            </div>
          </div>
          <div className="insight-card">
            <div className="insight-icon">🏦</div>
            <div className="insight-content">
              <h4>Platforms</h4>
              <p>{Object.keys(brokerData.datasets[0].data).length} different platforms and accounts</p>
            </div>
          </div>
          <div className="insight-card">
            <div className="insight-icon">⚖️</div>
            <div className="insight-content">
              <h4>Balance</h4>
              <p>Well-diversified across multiple asset classes and platforms</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiversificationAnalysis;

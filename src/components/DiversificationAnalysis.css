/* Diversification Analysis Styles */
.diversification-analysis {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.analysis-header {
  text-align: center;
  margin-bottom: 2rem;
}

.analysis-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.analysis-header p {
  margin: 0;
  color: #cbd5e1;
  font-size: 0.95rem;
  font-weight: 500;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(148, 163, 184, 0.2);
}

.chart-card h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.chart-container {
  height: 300px;
  position: relative;
}

.diversification-insights {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.diversification-insights h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.insight-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(30, 41, 59, 0.3);
  padding: 1rem;
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.insight-card:hover {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(148, 163, 184, 0.2);
  transform: translateY(-1px);
}

.insight-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.insight-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #f1f5f9;
}

.insight-content p {
  margin: 0;
  font-size: 0.85rem;
  color: #cbd5e1;
  line-height: 1.4;
}

/* Loading State */
.diversification-analysis.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #f1f5f9;
}

.diversification-analysis.loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.2);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.diversification-analysis.loading p {
  font-size: 1rem;
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .diversification-analysis {
    padding: 1.5rem;
  }
  
  .analysis-header h2 {
    font-size: 1.3rem;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .chart-card {
    padding: 1rem;
  }
  
  .chart-card h3 {
    font-size: 1.1rem;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .insight-card {
    padding: 0.75rem;
  }
  
  .insight-icon {
    font-size: 1.5rem;
  }
  
  .insight-content h4 {
    font-size: 0.9rem;
  }
  
  .insight-content p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .diversification-analysis {
    padding: 1rem;
  }
  
  .analysis-header h2 {
    font-size: 1.2rem;
  }
  
  .analysis-header p {
    font-size: 0.9rem;
  }
  
  .chart-card {
    padding: 0.75rem;
  }
  
  .chart-container {
    height: 200px;
  }
  
  .diversification-insights {
    padding: 1rem;
  }
  
  .insight-card {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
    padding: 1rem;
  }
  
  .insight-icon {
    font-size: 2rem;
  }
}

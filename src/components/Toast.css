/* Toast Notification Styles */
.toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  min-width: 300px;
  max-width: 400px;
  background: rgba(30, 41, 59, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
}

.toast-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  color: #f1f5f9;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast-close:hover {
  background: rgba(148, 163, 184, 0.2);
  color: #f1f5f9;
}

/* Toast Types */
.toast-success {
  border-left: 4px solid #34d399;
}

.toast-error {
  border-left: 4px solid #f87171;
}

.toast-info {
  border-left: 4px solid #60a5fa;
}

.toast-warning {
  border-left: 4px solid #fbbf24;
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    min-width: auto;
    max-width: none;
  }
  
  .toast-content {
    padding: 0.75rem 1rem;
  }
  
  .toast-message {
    font-size: 0.85rem;
  }
  
  .toast-icon {
    font-size: 1rem;
  }
  
  .toast-close {
    width: 20px;
    height: 20px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .toast {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
  }
  
  .toast-content {
    padding: 0.5rem 0.75rem;
  }
}

/* Account Details Styles */
.account-details {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.details-header {
  text-align: center;
  margin-bottom: 2rem;
}

.details-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.details-header p {
  margin: 0;
  color: #cbd5e1;
  font-size: 0.95rem;
  font-weight: 500;
}

.account-section {
  margin-bottom: 2rem;
}

.account-section:last-child {
  margin-bottom: 0;
}

.account-section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  border-bottom: 2px solid rgba(148, 163, 184, 0.2);
  padding-bottom: 0.5rem;
}

.account-card {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.account-card:hover {
  border-color: rgba(148, 163, 184, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.account-header:hover {
  background: rgba(148, 163, 184, 0.05);
}

.account-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #f1f5f9;
}

.account-type {
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.account-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.account-total {
  font-size: 1.1rem;
  font-weight: 700;
  color: #34d399;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.expand-icon {
  font-size: 0.8rem;
  color: #94a3b8;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.account-details {
  padding: 0 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  background: rgba(30, 41, 59, 0.2);
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #cbd5e1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.9rem;
  color: #e2e8f0;
  font-weight: 500;
}

.detail-value {
  font-size: 0.9rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.detail-value.cash {
  color: #fbbf24;
}

.detail-value.stock {
  color: #60a5fa;
}

.detail-value.fund {
  color: #34d399;
}

.detail-value.crypto {
  color: #a78bfa;
}

/* Loading State */
.account-details.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #f1f5f9;
}

.account-details.loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.2);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.account-details.loading p {
  font-size: 1rem;
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-details {
    padding: 1.5rem;
  }
  
  .details-header h2 {
    font-size: 1.3rem;
  }
  
  .account-header {
    padding: 1rem;
  }
  
  .account-info h4 {
    font-size: 1rem;
  }
  
  .account-total {
    font-size: 1rem;
  }
  
  .account-details {
    padding: 0 1rem 1rem 1rem;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .detail-value {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .account-details {
    padding: 1rem;
  }
  
  .details-header h2 {
    font-size: 1.2rem;
  }
  
  .account-section h3 {
    font-size: 1.1rem;
  }
  
  .account-header {
    padding: 0.75rem;
  }
  
  .account-summary {
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }
}

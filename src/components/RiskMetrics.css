/* Risk Metrics Styles */
.risk-metrics {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.metrics-header {
  text-align: center;
  margin-bottom: 2rem;
}

.metrics-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.metrics-header p {
  margin: 0;
  color: #cbd5e1;
  font-size: 0.95rem;
  font-weight: 500;
}

.metrics-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: rgba(15, 23, 42, 0.5);
  padding: 0.5rem;
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.tab-btn {
  background: transparent;
  border: none;
  color: #cbd5e1;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tab-btn:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #f1f5f9;
}

.tab-btn.active {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

.metrics-overview {
  animation: fadeIn 0.5s ease-out;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.1), transparent);
  transition: left 0.5s;
}

.metric-card:hover::before {
  left: 100%;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(148, 163, 184, 0.2);
}

.metric-card.primary {
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.15), rgba(59, 130, 246, 0.15));
  border: 1px solid rgba(96, 165, 250, 0.3);
}

.metric-header {
  margin-bottom: 1rem;
}

.metric-header h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #f1f5f9;
}

.metric-subtitle {
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  text-align: center;
}

.main-value {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 0.5rem;
}

.main-value.positive {
  color: #34d399;
}

.main-value.negative {
  color: #f87171;
}

.metric-description {
  font-size: 0.8rem;
  color: #cbd5e1;
  line-height: 1.4;
}

.returns-analysis,
.risk-analysis {
  animation: fadeIn 0.5s ease-out;
}

.chart-container {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  margin-bottom: 2rem;
}

.chart-container h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  text-align: center;
}

.chart-wrapper {
  height: 300px;
  position: relative;
}

.returns-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  font-size: 0.9rem;
  color: #cbd5e1;
  font-weight: 500;
}

.summary-item .value {
  font-size: 1rem;
  font-weight: 700;
  color: #f1f5f9;
}

.summary-item .value.positive {
  color: #34d399;
}

.summary-item .value.negative {
  color: #f87171;
}

.risk-insights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.insight-card {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  border-color: rgba(148, 163, 184, 0.2);
}

.insight-card h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #f1f5f9;
}

.insight-card p {
  margin: 0;
  font-size: 0.9rem;
  color: #cbd5e1;
  line-height: 1.5;
}

/* Loading State */
.risk-metrics.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #f1f5f9;
}

.risk-metrics.loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.2);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.risk-metrics.loading p {
  font-size: 1rem;
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .risk-metrics {
    padding: 1.5rem;
  }
  
  .metrics-header h2 {
    font-size: 1.3rem;
  }
  
  .metrics-tabs {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .tab-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .metric-card {
    padding: 1rem;
  }
  
  .main-value {
    font-size: 1.5rem;
  }
  
  .chart-wrapper {
    height: 250px;
  }
  
  .returns-summary {
    grid-template-columns: 1fr;
  }
  
  .risk-insights {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .risk-metrics {
    padding: 1rem;
  }
  
  .metrics-header h2 {
    font-size: 1.2rem;
  }
  
  .metric-card {
    padding: 0.75rem;
  }
  
  .main-value {
    font-size: 1.3rem;
  }
  
  .chart-wrapper {
    height: 200px;
  }
}

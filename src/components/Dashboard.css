/* Dashboard Styles */
.dashboard {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
  position: relative;
  overflow-x: hidden;
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.dashboard-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  padding: 1rem 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.header-content h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #f1f5f9, #cbd5e1, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.last-updated {
  display: flex;
  align-items: center;
}

.update-time {
  font-size: 0.8rem;
  color: #94a3b8;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  background: rgba(15, 23, 42, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #059669, #10b981);
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3);
  border: 1px solid rgba(16, 185, 129, 0.3);
  position: relative;
  overflow: hidden;
}

.refresh-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.refresh-btn:hover::before {
  left: 100%;
}

.refresh-btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
  background: linear-gradient(135deg, #10b981, #34d399);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn.refreshing .refresh-icon {
  animation: refreshSpin 1s linear infinite;
}

.refresh-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

@keyframes refreshSpin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.currency-toggle {
  background: linear-gradient(135deg, #1e293b, #334155, #475569);
  color: #e2e8f0;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.3);
  position: relative;
  overflow: hidden;
}

.currency-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.2), transparent);
  transition: left 0.5s;
}

.currency-toggle:hover::before {
  left: 100%;
}

.currency-toggle:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, #334155, #475569, #64748b);
  border: 1px solid rgba(148, 163, 184, 0.5);
}

.dashboard-nav {
  display: flex;
  gap: 0.5rem;
  background: rgba(15, 23, 42, 0.5);
  padding: 0.5rem;
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  overflow-x: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: none;
  color: #cbd5e1;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.1), transparent);
  transition: left 0.5s;
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #f1f5f9;
  transform: translateY(-1px);
}

.nav-item.active {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

.nav-item.active:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  transform: translateY(-1px);
}

.nav-icon {
  font-size: 1.1rem;
}

.nav-label {
  font-weight: 600;
}

.dashboard-content {
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Loading and Error States */
.dashboard-loading,
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #f1f5f9;
}

.dashboard-loading .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(148, 163, 184, 0.2);
  border-top: 4px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.dashboard-loading p {
  font-size: 1.2rem;
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.error-message {
  background: rgba(30, 41, 59, 0.5);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.error-message h3 {
  color: #f87171;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.error-message p {
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.error-message button {
  background: #60a5fa;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.error-message button:hover {
  background: #3b82f6;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
    text-align: center;
  }

  .header-controls {
    justify-content: center;
    gap: 0.75rem;
  }

  .refresh-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }

  .update-time {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
  }

  .dashboard-nav {
    justify-content: center;
    padding: 0.4rem;
  }

  .nav-item {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .nav-icon {
    font-size: 1rem;
  }

  .dashboard-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.75rem;
  }

  .header-content h1 {
    font-size: 1.3rem;
  }

  .currency-toggle {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .refresh-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .last-updated {
    order: -1;
    width: 100%;
    justify-content: center;
  }

  .update-time {
    font-size: 0.7rem;
    padding: 0.3rem 0.5rem;
  }

  .dashboard-nav {
    gap: 0.25rem;
    padding: 0.3rem;
  }

  .nav-item {
    padding: 0.5rem 0.6rem;
    font-size: 0.8rem;
  }

  .nav-label {
    display: none;
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .dashboard-content {
    padding: 0.5rem;
  }
}
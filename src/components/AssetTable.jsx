import React, { useState, useEffect } from 'react';
import { getAssetBreakdown } from '../utils/portfolioCalculations';
import { formatCurrency, formatPercentage } from '../services/currencyService';
import './AssetTable.css';

const AssetTable = ({ portfolioData, currency }) => {
  const [assets, setAssets] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'absoluteGL', direction: 'desc' });
  const [filterType, setFilterType] = useState('All');

  useEffect(() => {
    if (portfolioData) {
      const assetData = getAssetBreakdown(portfolioData, currency);
      setAssets(assetData);
    }
  }, [portfolioData, currency]);

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedAssets = React.useMemo(() => {
    let sortableAssets = [...assets];

    // Filter by type
    if (filterType !== 'All') {
      sortableAssets = sortableAssets.filter(asset => asset.type === filterType);
    }

    // Sort
    if (sortConfig.key) {
      sortableAssets.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return sortableAssets;
  }, [assets, sortConfig, filterType]);

  const getSortIcon = (columnName) => {
    if (sortConfig.key === columnName) {
      return sortConfig.direction === 'asc' ? '↑' : '↓';
    }
    return '↕';
  };

  const getAssetTypes = () => {
    const types = ['All', ...new Set(assets.map(asset => asset.type))];
    return types;
  };

  const getRowClassName = (asset) => {
    if (asset.absoluteGL > 0) return 'positive';
    if (asset.absoluteGL < 0) return 'negative';
    return 'neutral';
  };

  if (!assets.length) {
    return (
      <div className="asset-table loading">
        <div className="loading-spinner"></div>
        <p>Loading asset data...</p>
      </div>
    );
  }

  return (
    <div className="asset-table">
      <div className="table-header">
        <h2>Asset Breakdown</h2>
        <div className="table-controls">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            {getAssetTypes().map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
          <span className="asset-count">
            {sortedAssets.length} asset{sortedAssets.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      <div className="table-container">
        <table className="assets-table">
          <thead>
            <tr>
              <th onClick={() => handleSort('name')} className="sortable">
                Asset {getSortIcon('name')}
              </th>
              <th onClick={() => handleSort('type')} className="sortable">
                Type {getSortIcon('type')}
              </th>
              <th onClick={() => handleSort('broker')} className="sortable">
                Broker {getSortIcon('broker')}
              </th>
              <th onClick={() => handleSort('invested')} className="sortable">
                Invested {getSortIcon('invested')}
              </th>
              <th onClick={() => handleSort('current')} className="sortable">
                Current {getSortIcon('current')}
              </th>
              <th onClick={() => handleSort('absoluteGL')} className="sortable">
                Gain/Loss {getSortIcon('absoluteGL')}
              </th>
              <th onClick={() => handleSort('percentageGL')} className="sortable">
                % Change {getSortIcon('percentageGL')}
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedAssets.map((asset, index) => (
              <tr key={`${asset.broker}-${asset.name}-${index}`} className={getRowClassName(asset)}>
                <td className="asset-name">
                  <div className="asset-info">
                    <span className="name">{asset.name}</span>
                    {asset.shares && (
                      <span className="shares">{asset.shares} shares</span>
                    )}
                    {asset.units && (
                      <span className="units">{asset.units.toLocaleString()} units</span>
                    )}
                    {asset.coins && (
                      <span className="coins">{asset.coins.toLocaleString()} coins</span>
                    )}
                  </div>
                </td>
                <td className="asset-type">
                  <span className={`type-badge ${asset.type.toLowerCase().replace(/\s+/g, '-')}`}>
                    {asset.type}
                  </span>
                </td>
                <td className="broker">{asset.broker}</td>
                <td className="invested">{formatCurrency(asset.invested, currency)}</td>
                <td className="current">{formatCurrency(asset.current, currency)}</td>
                <td className={`gain-loss ${asset.absoluteGL >= 0 ? 'positive' : 'negative'}`}>
                  {formatCurrency(asset.absoluteGL, currency)}
                </td>
                <td className={`percentage ${asset.percentageGL >= 0 ? 'positive' : 'negative'}`}>
                  {formatPercentage(asset.percentageGL).value}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {sortedAssets.length === 0 && filterType !== 'All' && (
        <div className="no-results">
          <p>No {filterType.toLowerCase()} assets found.</p>
        </div>
      )}
    </div>
  );
};

export default AssetTable;

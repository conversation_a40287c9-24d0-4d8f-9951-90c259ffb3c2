import React, { useState, useEffect } from 'react';
import { fetchPortfolioDataWithFallback } from '../services/firebaseService';
import { getPortfolioSummary } from '../utils/portfolioCalculations';
import { formatCurrency, formatPercentage, toggleCurrency } from '../services/currencyService';
import './PortfolioSummary.css';

const PortfolioSummary = () => {
  const [portfolioData, setPortfolioData] = useState(null);
  const [currency, setCurrency] = useState('INR');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [summary, setSummary] = useState(null);

  // Fetch portfolio data on component mount
  useEffect(() => {
    const loadPortfolioData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchPortfolioDataWithFallback();
        setPortfolioData(data);
      } catch (err) {
        setError('Failed to load portfolio data');
        console.error('Error loading portfolio data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadPortfolioData();
  }, []);

  // Calculate summary when portfolio data or currency changes
  useEffect(() => {
    if (portfolioData) {
      const calculatedSummary = getPortfolioSummary(portfolioData, currency);
      setSummary(calculatedSummary);
    }
  }, [portfolioData, currency]);

  const handleCurrencyToggle = () => {
    setCurrency(prevCurrency => toggleCurrency(prevCurrency));
  };

  if (loading) {
    return (
      <div className="portfolio-summary loading">
        <div className="loading-spinner"></div>
        <p>Loading portfolio data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="portfolio-summary error">
        <div className="error-message">
          <h3>Error</h3>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="portfolio-summary">
        <p>No portfolio data available</p>
      </div>
    );
  }

  const percentageInfo = formatPercentage(summary.percentageChange);

  return (
    <div className="portfolio-summary">
      <div className="portfolio-header">
        <h1>Net Worth Overview</h1>
        <button
          className="currency-toggle"
          onClick={handleCurrencyToggle}
          title={`Switch to ${currency === 'INR' ? 'USD' : 'INR'}`}
        >
          {currency === 'INR' ? '₹' : '$'} {currency}
        </button>
      </div>

      <div className="summary-grid">
        {/* Net Worth Card */}
        <div className="summary-card net-worth-card">
          <div className="card-header">
            <h2>Net Worth</h2>
            <span className="card-subtitle">Total Portfolio Value</span>
          </div>
          <div className="card-value">
            <span className="main-value">
              {formatCurrency(summary.netWorth, currency, false)}
            </span>
          </div>
        </div>

        {/* Total Invested vs Current Value Card */}
        <div className="summary-card investment-card">
          <div className="card-header">
            <h2>Investment Overview</h2>
            <span className="card-subtitle">Invested vs Current</span>
          </div>
          <div className="investment-details">
            <div className="investment-row">
              <span className="label">Total Invested:</span>
              <span className="value invested">
                {formatCurrency(summary.totalInvested, currency)}
              </span>
            </div>
            <div className="investment-row">
              <span className="label">Current Value:</span>
              <span className="value current">
                {formatCurrency(summary.currentValue, currency)}
              </span>
            </div>
          </div>
        </div>

        {/* Unrealized P&L Card */}
        <div className={`summary-card pnl-card ${percentageInfo.isPositive ? 'positive-pnl' : ''}`}>
          <div className="card-header">
            <h2>Unrealized P&L</h2>
            <span className="card-subtitle">Profit & Loss</span>
          </div>
          <div className="card-value">
            <span className={`main-value ${percentageInfo.isPositive ? 'positive' : percentageInfo.isNegative ? 'negative' : 'neutral'}`}>
              {formatCurrency(summary.unrealizedPL, currency)}
            </span>
          </div>
        </div>

        {/* Percentage Change Card */}
        <div className="summary-card percentage-card">
          <div className="card-header">
            <h2>Total Return</h2>
            <span className="card-subtitle">Overall Performance</span>
          </div>
          <div className="card-value">
            <span className={`main-value ${percentageInfo.isPositive ? 'positive' : percentageInfo.isNegative ? 'negative' : 'neutral'}`}>
              {percentageInfo.value}
            </span>
          </div>
          <div className="percentage-indicator">
            <div className={`indicator-bar ${percentageInfo.isPositive ? 'positive' : percentageInfo.isNegative ? 'negative' : 'neutral'}`}>
              <div
                className="indicator-fill"
                style={{ width: `${Math.min(Math.abs(summary.percentageChange), 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="quick-stats">
        <div className="stat-item">
          <span className="stat-label">Currency</span>
          <span className="stat-value">{currency}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Last Updated</span>
          <span className="stat-value">{new Date().toLocaleDateString()}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Status</span>
          <span className={`stat-value status ${percentageInfo.isPositive ? 'positive' : percentageInfo.isNegative ? 'negative' : 'neutral'}`}>
            {percentageInfo.isPositive ? 'Gaining' : percentageInfo.isNegative ? 'Losing' : 'Stable'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PortfolioSummary;

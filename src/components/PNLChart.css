/* PNL Chart Styles */
.pnl-chart {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.pnl-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.1), transparent);
  transition: left 0.5s;
}

.pnl-chart:hover::before {
  left: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chart-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.time-range-selector {
  display: flex;
  gap: 0.5rem;
  background: rgba(15, 23, 42, 0.5);
  padding: 0.5rem;
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.time-range-btn {
  background: transparent;
  border: none;
  color: #cbd5e1;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.time-range-btn:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #f1f5f9;
}

.time-range-btn.active {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

.chart-container {
  height: 400px;
  position: relative;
  background: rgba(15, 23, 42, 0.3);
  border-radius: 16px;
  padding: 1rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Loading State */
.pnl-chart.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #f1f5f9;
}

.pnl-chart.loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.2);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pnl-chart.loading p {
  font-size: 1rem;
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pnl-chart {
    padding: 1.5rem;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }
  
  .chart-header h2 {
    text-align: center;
    font-size: 1.3rem;
  }
  
  .time-range-selector {
    justify-content: center;
  }
  
  .time-range-btn {
    flex: 1;
    text-align: center;
  }
  
  .chart-container {
    height: 300px;
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .pnl-chart {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .chart-header h2 {
    font-size: 1.2rem;
  }
  
  .time-range-selector {
    padding: 0.3rem;
  }
  
  .time-range-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .chart-container {
    height: 250px;
  }
}

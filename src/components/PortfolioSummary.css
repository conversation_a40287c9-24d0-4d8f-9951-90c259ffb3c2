/* Portfolio Summary Styles */
.portfolio-summary {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

/* Header */
.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.portfolio-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.currency-toggle {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.currency-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Summary Cards */
.summary-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.card-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.main-value {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

/* Net Worth Card */
.net-worth-card .main-value {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Investment Card */
.investment-details {
  space-y: 1rem;
}

.investment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.investment-row:last-child {
  border-bottom: none;
}

.investment-row .label {
  font-weight: 500;
  color: #4a5568;
}

.investment-row .value {
  font-weight: 600;
  font-size: 1.125rem;
}

.investment-row .value.invested {
  color: #3182ce;
}

.investment-row .value.current {
  color: #38a169;
}

/* P&L and Percentage Colors */
.positive {
  color: #38a169 !important;
}

.negative {
  color: #e53e3e !important;
}

.neutral {
  color: #718096 !important;
}

/* Percentage Indicator */
.percentage-indicator {
  margin-top: 1rem;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.indicator-bar {
  height: 100%;
  position: relative;
  border-radius: 4px;
}

.indicator-bar.positive {
  background: linear-gradient(90deg, #68d391, #38a169);
}

.indicator-bar.negative {
  background: linear-gradient(90deg, #fc8181, #e53e3e);
}

.indicator-bar.neutral {
  background: #cbd5e0;
}

.indicator-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Quick Stats */
.quick-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.125rem;
  font-weight: 600;
  color: #2d3748;
}

.stat-value.status.positive {
  color: #38a169;
}

.stat-value.status.negative {
  color: #e53e3e;
}

.stat-value.status.neutral {
  color: #718096;
}

/* Loading State */
.portfolio-summary.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.portfolio-summary.error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.error-message {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.error-message h3 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error-message button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .portfolio-summary {
    padding: 1rem;
  }
  
  .portfolio-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .portfolio-header h1 {
    font-size: 2rem;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .summary-card {
    padding: 1.5rem;
  }
  
  .main-value {
    font-size: 2rem;
  }
  
  .quick-stats {
    flex-direction: column;
    gap: 1rem;
  }
}

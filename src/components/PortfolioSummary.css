/* Portfolio Summary Styles */
.portfolio-summary {
  width: 100%;
  margin: 0;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  min-height: 100vh;
  color: #333;
  position: relative;
  overflow-x: hidden;
  animation: backgroundShift 10s ease-in-out infinite;
}

@keyframes backgroundShift {

  0%,
  100% {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  }

  50% {
    background: linear-gradient(135deg, #f093fb 0%, #667eea 50%, #764ba2 100%);
  }
}

.portfolio-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

/* Header */
.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 1.5rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
  animation: slideInFromTop 0.8s ease-out;
  transition: all 0.3s ease;
}

.portfolio-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.2);
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.portfolio-header h1 {
  margin: 0;
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.8));
  }
}

.currency-toggle {
  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  background-size: 300% 300%;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.currency-toggle:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

.currency-toggle:active {
  transform: translateY(-1px) scale(1.02);
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

/* Summary Cards */
.summary-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.summary-card:nth-child(1) {
  animation-delay: 0.1s;
}

.summary-card:nth-child(2) {
  animation-delay: 0.2s;
}

.summary-card:nth-child(3) {
  animation-delay: 0.3s;
}

.summary-card:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.summary-card:hover::before {
  left: 100%;
}

.summary-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.card-header {
  margin-bottom: 2rem;
  position: relative;
}

.card-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: textShimmer 2s ease-in-out infinite alternate;
}

@keyframes textShimmer {
  0% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  100% {
    text-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
  }
}

.card-subtitle {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.card-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  position: relative;
}

.main-value {
  font-size: 2.8rem;
  font-weight: 800;
  text-align: center;
  color: #ffffff;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: valueGlow 3s ease-in-out infinite alternate;
}

@keyframes valueGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
  }
}

/* Net Worth Card */
.net-worth-card {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
  border: 2px solid rgba(102, 126, 234, 0.3);
}

.net-worth-card .main-value {
  background: linear-gradient(135deg, #ffd700, #ffed4e, #ffd700);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: goldShimmer 2s ease-in-out infinite;
}

@keyframes goldShimmer {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* Investment Card */
.investment-card {
  background: linear-gradient(135deg, rgba(56, 161, 105, 0.2), rgba(49, 130, 206, 0.2));
  border: 2px solid rgba(56, 161, 105, 0.3);
}

.investment-details {
  gap: 1.5rem;
}

.investment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: rowSlideIn 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.investment-row:nth-child(1) {
  animation-delay: 0.1s;
}

.investment-row:nth-child(2) {
  animation-delay: 0.2s;
}

@keyframes rowSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.investment-row:last-child {
  border-bottom: none;
}

.investment-row:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem 1rem;
  margin: 0 -1rem;
}

.investment-row .label {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.investment-row .value {
  font-weight: 700;
  font-size: 1.2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.investment-row .value.invested {
  color: #63b3ed;
  animation: pulseBlue 2s ease-in-out infinite alternate;
}

.investment-row .value.current {
  color: #68d391;
  animation: pulseGreen 2s ease-in-out infinite alternate;
}

@keyframes pulseBlue {
  0% {
    filter: drop-shadow(0 0 5px rgba(99, 179, 237, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 15px rgba(99, 179, 237, 0.8));
  }
}

@keyframes pulseGreen {
  0% {
    filter: drop-shadow(0 0 5px rgba(104, 211, 145, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 15px rgba(104, 211, 145, 0.8));
  }
}

/* P&L Card */
.pnl-card {
  background: linear-gradient(135deg, rgba(229, 62, 62, 0.2), rgba(245, 101, 101, 0.2));
  border: 2px solid rgba(229, 62, 62, 0.3);
}

.pnl-card.positive-pnl {
  background: linear-gradient(135deg, rgba(56, 161, 105, 0.2), rgba(104, 211, 145, 0.2));
  border: 2px solid rgba(56, 161, 105, 0.3);
}

/* Percentage Card */
.percentage-card {
  background: linear-gradient(135deg, rgba(128, 90, 213, 0.2), rgba(165, 102, 255, 0.2));
  border: 2px solid rgba(128, 90, 213, 0.3);
}

/* P&L and Percentage Colors */
.positive {
  color: #68d391 !important;
  animation: positiveGlow 2s ease-in-out infinite alternate;
}

.negative {
  color: #fc8181 !important;
  animation: negativeGlow 2s ease-in-out infinite alternate;
}

.neutral {
  color: #cbd5e0 !important;
}

@keyframes positiveGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(104, 211, 145, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(104, 211, 145, 0.8));
  }
}

@keyframes negativeGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(252, 129, 129, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(252, 129, 129, 0.8));
  }
}

/* Percentage Indicator */
.percentage-indicator {
  margin-top: 1.5rem;
  height: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.indicator-bar {
  height: 100%;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}

.indicator-bar.positive {
  background: linear-gradient(90deg, #68d391, #38a169, #68d391);
  background-size: 200% 100%;
  animation: positiveFlow 3s ease-in-out infinite;
}

.indicator-bar.negative {
  background: linear-gradient(90deg, #fc8181, #e53e3e, #fc8181);
  background-size: 200% 100%;
  animation: negativeFlow 3s ease-in-out infinite;
}

.indicator-bar.neutral {
  background: #cbd5e0;
}

@keyframes positiveFlow {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes negativeFlow {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.indicator-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 20px;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.indicator-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

/* Quick Stats */
.quick-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
  animation: statsSlideUp 0.8s ease-out 0.5s forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes statsSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  text-align: center;
  transition: all 0.3s ease;
  padding: 1rem;
  border-radius: 16px;
  position: relative;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
}

.stat-label {
  display: block;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: statValuePulse 3s ease-in-out infinite alternate;
}

@keyframes statValuePulse {
  0% {
    filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3));
  }

  100% {
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.6));
  }
}

.stat-value.status.positive {
  color: #68d391;
  animation: positiveStatusGlow 2s ease-in-out infinite alternate;
}

.stat-value.status.negative {
  color: #fc8181;
  animation: negativeStatusGlow 2s ease-in-out infinite alternate;
}

.stat-value.status.neutral {
  color: #cbd5e0;
}

@keyframes positiveStatusGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(104, 211, 145, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(104, 211, 145, 0.8));
  }
}

@keyframes negativeStatusGlow {
  0% {
    filter: drop-shadow(0 0 5px rgba(252, 129, 129, 0.5));
  }

  100% {
    filter: drop-shadow(0 0 20px rgba(252, 129, 129, 0.8));
  }
}

/* Loading State */
.portfolio-summary.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
  position: relative;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid rgba(255, 255, 255, 0.2);
  border-top: 6px solid #ffffff;
  border-right: 6px solid #ff6b6b;
  border-bottom: 6px solid #feca57;
  border-left: 6px solid #48dbfb;
  border-radius: 50%;
  animation: rainbowSpin 1.5s linear infinite;
  margin-bottom: 2rem;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border: 3px solid transparent;
  border-top: 3px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: rainbowSpin 1s linear infinite reverse;
}

@keyframes rainbowSpin {
  0% {
    transform: rotate(0deg);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
  }

  25% {
    filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.5));
  }

  50% {
    filter: drop-shadow(0 0 20px rgba(254, 202, 87, 0.5));
  }

  75% {
    filter: drop-shadow(0 0 20px rgba(72, 219, 251, 0.5));
  }

  100% {
    transform: rotate(360deg);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
  }
}

.portfolio-summary.loading p {
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: loadingTextPulse 2s ease-in-out infinite alternate;
}

@keyframes loadingTextPulse {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }

  100% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Error State */
.portfolio-summary.error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.error-message {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.error-message h3 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error-message button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .portfolio-summary {
    padding: 1.5rem;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .portfolio-summary {
    padding: 1rem;
  }

  .portfolio-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
  }

  .portfolio-header h1 {
    font-size: 2.2rem;
  }

  .currency-toggle {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .summary-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .summary-card {
    padding: 2rem;
  }

  .main-value {
    font-size: 2.2rem;
  }

  .quick-stats {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .stat-item {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .portfolio-summary {
    padding: 0.5rem;
  }

  .portfolio-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .portfolio-header h1 {
    font-size: 1.8rem;
  }

  .currency-toggle {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }

  .summary-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .card-header h2 {
    font-size: 1.2rem;
  }

  .main-value {
    font-size: 1.8rem;
  }

  .investment-row {
    padding: 0.8rem 0;
  }

  .investment-row .value {
    font-size: 1rem;
  }

  .quick-stats {
    padding: 1rem;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.1rem;
  }
}
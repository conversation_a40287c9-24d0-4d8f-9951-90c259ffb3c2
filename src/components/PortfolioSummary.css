/* Portfolio Summary Styles */
.portfolio-summary {
  width: 100%;
  margin: 0;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
  min-height: 100vh;
  color: #e2e8f0;
  position: relative;
  overflow-x: hidden;
}

.portfolio-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 226, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.portfolio-summary::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="12" cy="12" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

/* Header */
.portfolio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: rgba(30, 41, 59, 0.4);
  padding: 1.5rem 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  position: relative;
  z-index: 1;
  animation: slideInFromTop 0.8s ease-out;
  transition: all 0.3s ease;
}

.portfolio-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.portfolio-header h1 {
  margin: 0;
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #f1f5f9, #cbd5e1, #94a3b8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.currency-toggle {
  background: linear-gradient(135deg, #1e293b, #334155, #475569);
  color: #e2e8f0;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(148, 163, 184, 0.3);
  position: relative;
  overflow: hidden;
}

.currency-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.2), transparent);
  transition: left 0.5s;
}

.currency-toggle:hover::before {
  left: 100%;
}

.currency-toggle:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
  background: linear-gradient(135deg, #334155, #475569, #64748b);
  border: 1px solid rgba(148, 163, 184, 0.5);
}

.currency-toggle:active {
  transform: translateY(-1px) scale(1.02);
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

/* Summary Cards */
.summary-card {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  animation: cardSlideIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.summary-card:nth-child(1) {
  animation-delay: 0.1s;
}

.summary-card:nth-child(2) {
  animation-delay: 0.2s;
}

.summary-card:nth-child(3) {
  animation-delay: 0.3s;
}

.summary-card:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.1), transparent);
  transition: left 0.5s;
}

.summary-card:hover::before {
  left: 100%;
}

.summary-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
}

.card-header {
  margin-bottom: 2rem;
  position: relative;
}

.card-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.card-subtitle {
  font-size: 0.95rem;
  color: #cbd5e1;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-value {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  position: relative;
}

.main-value {
  font-size: 2.8rem;
  font-weight: 800;
  text-align: center;
  color: #f1f5f9;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

/* Net Worth Card */
.net-worth-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.15));
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.net-worth-card .main-value {
  background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(251, 191, 36, 0.3));
}

/* Investment Card */
.investment-card {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(59, 130, 246, 0.15));
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.investment-details {
  gap: 1.5rem;
}

.investment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  transition: all 0.3s ease;
  animation: rowSlideIn 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.investment-row:nth-child(1) {
  animation-delay: 0.1s;
}

.investment-row:nth-child(2) {
  animation-delay: 0.2s;
}

@keyframes rowSlideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.investment-row:last-child {
  border-bottom: none;
}

.investment-row:hover {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 1rem 1rem;
  margin: 0 -1rem;
}

.investment-row .label {
  font-weight: 600;
  color: #cbd5e1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.investment-row .value {
  font-weight: 700;
  font-size: 1.2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.investment-row .value.invested {
  color: #60a5fa;
}

.investment-row .value.current {
  color: #34d399;
}

/* P&L Card */
.pnl-card {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(248, 113, 113, 0.15));
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.pnl-card.positive-pnl {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(74, 222, 128, 0.15));
  border: 1px solid rgba(34, 197, 94, 0.3);
}

/* Percentage Card */
.percentage-card {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(168, 85, 247, 0.15));
  border: 1px solid rgba(139, 92, 246, 0.3);
}

/* P&L and Percentage Colors */
.positive {
  color: #34d399 !important;
  filter: drop-shadow(0 2px 4px rgba(52, 211, 153, 0.3));
}

.negative {
  color: #f87171 !important;
  filter: drop-shadow(0 2px 4px rgba(248, 113, 113, 0.3));
}

.neutral {
  color: #94a3b8 !important;
}

/* Percentage Indicator */
.percentage-indicator {
  margin-top: 1.5rem;
  height: 12px;
  background: rgba(30, 41, 59, 0.5);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.4);
}

.indicator-bar {
  height: 100%;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}

.indicator-bar.positive {
  background: linear-gradient(90deg, #34d399, #10b981);
}

.indicator-bar.negative {
  background: linear-gradient(90deg, #f87171, #ef4444);
}

.indicator-bar.neutral {
  background: #64748b;
}

.indicator-fill {
  height: 100%;
  background: rgba(148, 163, 184, 0.3);
  border-radius: 20px;
  transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.indicator-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

/* Quick Stats */
.quick-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(30, 41, 59, 0.3);
  padding: 2rem;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  position: relative;
  z-index: 1;
  animation: statsSlideUp 0.8s ease-out 0.5s forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes statsSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  text-align: center;
  transition: all 0.3s ease;
  padding: 1rem;
  border-radius: 16px;
  position: relative;
}

.stat-item:hover {
  background: rgba(148, 163, 184, 0.1);
  transform: translateY(-5px);
}

.stat-label {
  display: block;
  font-size: 0.95rem;
  color: #cbd5e1;
  font-weight: 600;
  margin-bottom: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 1.3rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.stat-value.status.positive {
  color: #34d399;
}

.stat-value.status.negative {
  color: #f87171;
}

.stat-value.status.neutral {
  color: #94a3b8;
}

/* Loading State */
.portfolio-summary.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: white;
  position: relative;
}

.loading-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid rgba(148, 163, 184, 0.2);
  border-top: 6px solid #f1f5f9;
  border-right: 6px solid #60a5fa;
  border-bottom: 6px solid #34d399;
  border-left: 6px solid #a78bfa;
  border-radius: 50%;
  animation: darkSpin 1.5s linear infinite;
  margin-bottom: 2rem;
  position: relative;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border: 3px solid transparent;
  border-top: 3px solid rgba(241, 245, 249, 0.5);
  border-radius: 50%;
  animation: darkSpin 1s linear infinite reverse;
}

@keyframes darkSpin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.portfolio-summary.loading p {
  font-size: 1.2rem;
  font-weight: 600;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Error State */
.portfolio-summary.error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.error-message {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.error-message h3 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error-message button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .portfolio-summary {
    padding: 1.5rem;
  }

  .summary-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .portfolio-summary {
    padding: 1rem;
  }

  .portfolio-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    padding: 1.5rem;
  }

  .portfolio-header h1 {
    font-size: 2.2rem;
  }

  .currency-toggle {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .summary-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .summary-card {
    padding: 2rem;
  }

  .main-value {
    font-size: 2.2rem;
  }

  .quick-stats {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .stat-item {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .portfolio-summary {
    padding: 0.5rem;
  }

  .portfolio-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .portfolio-header h1 {
    font-size: 1.8rem;
  }

  .currency-toggle {
    padding: 0.7rem 1.2rem;
    font-size: 0.9rem;
  }

  .summary-card {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .card-header h2 {
    font-size: 1.2rem;
  }

  .main-value {
    font-size: 1.8rem;
  }

  .investment-row {
    padding: 0.8rem 0;
  }

  .investment-row .value {
    font-size: 1rem;
  }

  .quick-stats {
    padding: 1rem;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.1rem;
  }
}
import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { getPortfolioSummary } from '../utils/portfolioCalculations';
import { formatCurrency } from '../services/currencyService';
import './PNLChart.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const PNLChart = ({ portfolioData, currency }) => {
  const [chartData, setChartData] = useState(null);
  const [timeRange, setTimeRange] = useState('6M'); // 1M, 3M, 6M, 1Y, ALL

  const generateChartData = () => {
    // Generate mock historical data for demonstration
    // In a real app, this would come from your backend
    const summary = getPortfolioSummary(portfolioData, currency);
    const currentDate = new Date();
    const dataPoints = getDataPointsForRange(timeRange);

    const dates = [];
    const investedValues = [];
    const currentValues = [];

    for (let i = dataPoints - 1; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setDate(date.getDate() - (i * getDaysInterval(timeRange)));
      dates.push(date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        ...(timeRange === '1Y' || timeRange === 'ALL' ? { year: '2-digit' } : {})
      }));

      // Simulate historical data with some variance
      const progressRatio = (dataPoints - 1 - i) / (dataPoints - 1);
      const investedValue = summary.totalInvested * (0.3 + progressRatio * 0.7);
      const variance = 0.95 + Math.random() * 0.1; // ±5% variance
      const currentValue = investedValue * variance * (summary.currentInvestmentValue / summary.totalInvested);

      investedValues.push(investedValue);
      currentValues.push(currentValue);
    }

    const data = {
      labels: dates,
      datasets: [
        {
          label: 'Total Invested',
          data: investedValues,
          borderColor: '#60a5fa',
          backgroundColor: 'rgba(96, 165, 250, 0.1)',
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          pointBackgroundColor: '#60a5fa',
          pointBorderColor: '#1e40af',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
        {
          label: 'Current Value',
          data: currentValues,
          borderColor: '#34d399',
          backgroundColor: 'rgba(52, 211, 153, 0.1)',
          borderWidth: 3,
          fill: '+1',
          tension: 0.4,
          pointBackgroundColor: '#34d399',
          pointBorderColor: '#059669',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
        }
      ]
    };

    setChartData(data);
  };

  const getDataPointsForRange = (range) => {
    switch (range) {
      case '1M': return 30;
      case '3M': return 90;
      case '6M': return 180;
      case '1Y': return 365;
      case 'ALL': return 500;
      default: return 180;
    }
  };

  const getDaysInterval = (range) => {
    switch (range) {
      case '1M': return 1;
      case '3M': return 1;
      case '6M': return 1;
      case '1Y': return 1;
      case 'ALL': return 2;
      default: return 1;
    }
  };

  useEffect(() => {
    if (portfolioData) {
      generateChartData();
    }
  }, [portfolioData, currency, timeRange]);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#f1f5f9',
          font: {
            size: 14,
            weight: 'bold'
          },
          usePointStyle: true,
          pointStyle: 'circle'
        }
      },
      title: {
        display: true,
        text: 'Portfolio Value Over Time',
        color: '#f1f5f9',
        font: {
          size: 18,
          weight: 'bold'
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(30, 41, 59, 0.9)',
        titleColor: '#f1f5f9',
        bodyColor: '#cbd5e1',
        borderColor: 'rgba(148, 163, 184, 0.3)',
        borderWidth: 1,
        callbacks: {
          label: function (context) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y, currency)}`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(148, 163, 184, 0.1)',
          borderColor: 'rgba(148, 163, 184, 0.3)'
        },
        ticks: {
          color: '#cbd5e1',
          font: {
            size: 12
          }
        }
      },
      y: {
        grid: {
          color: 'rgba(148, 163, 184, 0.1)',
          borderColor: 'rgba(148, 163, 184, 0.3)'
        },
        ticks: {
          color: '#cbd5e1',
          font: {
            size: 12
          },
          callback: function (value) {
            return formatCurrency(value, currency, false);
          }
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    },
    elements: {
      point: {
        hoverBackgroundColor: '#ffffff'
      }
    }
  };

  const timeRanges = [
    { value: '1M', label: '1M' },
    { value: '3M', label: '3M' },
    { value: '6M', label: '6M' },
    { value: '1Y', label: '1Y' },
    { value: 'ALL', label: 'ALL' }
  ];

  if (!chartData) {
    return (
      <div className="pnl-chart loading">
        <div className="loading-spinner"></div>
        <p>Loading chart data...</p>
      </div>
    );
  }

  return (
    <div className="pnl-chart">
      <div className="chart-header">
        <h2>Portfolio Performance</h2>
        <div className="time-range-selector">
          {timeRanges.map(range => (
            <button
              key={range.value}
              className={`time-range-btn ${timeRange === range.value ? 'active' : ''}`}
              onClick={() => setTimeRange(range.value)}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>
      <div className="chart-container">
        <Line data={chartData} options={options} />
      </div>
    </div>
  );
};

export default PNLChart;

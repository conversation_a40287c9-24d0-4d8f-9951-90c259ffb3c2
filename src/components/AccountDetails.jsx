import React, { useState } from 'react';
import { formatCurrency, convertCurrency } from '../services/currencyService';
import './AccountDetails.css';

const AccountDetails = ({ portfolioData, currency }) => {
  const [expandedAccounts, setExpandedAccounts] = useState({});

  const toggleAccount = (accountId) => {
    setExpandedAccounts(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }));
  };

  const calculateAccountTotal = (account, accountType) => {
    let total = 0;
    
    if (accountType === 'bank') {
      return currency === 'INR' ? account : convertCurrency(account, 'INR', 'USD');
    }
    
    // For brokers
    if (account.stocks_inr) {
      account.stocks_inr.forEach(stock => {
        total += currency === 'INR' ? stock.current : convertCurrency(stock.current, 'INR', 'USD');
      });
    }
    
    if (account.stocks_usd) {
      account.stocks_usd.forEach(stock => {
        total += currency === 'USD' ? stock.current : convertCurrency(stock.current, 'USD', 'INR');
      });
    }
    
    if (account.mutual_funds) {
      account.mutual_funds.forEach(fund => {
        total += currency === 'INR' ? fund.current : convertCurrency(fund.current, 'INR', 'USD');
      });
    }
    
    if (account.cryptos_inr) {
      account.cryptos_inr.forEach(crypto => {
        total += currency === 'INR' ? crypto.current : convertCurrency(crypto.current, 'INR', 'USD');
      });
    }
    
    if (account.cryptos_usd) {
      account.cryptos_usd.forEach(crypto => {
        total += currency === 'USD' ? crypto.current : convertCurrency(crypto.current, 'USD', 'INR');
      });
    }
    
    if (account.funds_inr) {
      total += currency === 'INR' ? account.funds_inr : convertCurrency(account.funds_inr, 'INR', 'USD');
    }
    
    if (account.funds_usd) {
      total += currency === 'USD' ? account.funds_usd : convertCurrency(account.funds_usd, 'USD', 'INR');
    }
    
    return total;
  };

  const renderBankAccounts = () => {
    if (!portfolioData?.accounts?.bank_accounts) return null;
    
    return (
      <div className="account-section">
        <h3>Bank Accounts</h3>
        {Object.entries(portfolioData.accounts.bank_accounts).map(([bankName, balance]) => {
          const accountId = `bank-${bankName}`;
          const isExpanded = expandedAccounts[accountId];
          const total = calculateAccountTotal(balance, 'bank');
          
          return (
            <div key={accountId} className="account-card">
              <div 
                className="account-header"
                onClick={() => toggleAccount(accountId)}
              >
                <div className="account-info">
                  <h4>{bankName}</h4>
                  <span className="account-type">Bank Account</span>
                </div>
                <div className="account-summary">
                  <span className="account-total">{formatCurrency(total, currency)}</span>
                  <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
                </div>
              </div>
              
              {isExpanded && (
                <div className="account-details">
                  <div className="detail-row">
                    <span className="detail-label">Available Balance:</span>
                    <span className="detail-value cash">{formatCurrency(total, currency)}</span>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const renderBrokerAccounts = () => {
    if (!portfolioData?.accounts?.brokers) return null;
    
    return (
      <div className="account-section">
        <h3>Investment Accounts</h3>
        {Object.entries(portfolioData.accounts.brokers).map(([brokerName, account]) => {
          const accountId = `broker-${brokerName}`;
          const isExpanded = expandedAccounts[accountId];
          const total = calculateAccountTotal(account, 'broker');
          
          return (
            <div key={accountId} className="account-card">
              <div 
                className="account-header"
                onClick={() => toggleAccount(accountId)}
              >
                <div className="account-info">
                  <h4>{brokerName}</h4>
                  <span className="account-type">Investment Account</span>
                </div>
                <div className="account-summary">
                  <span className="account-total">{formatCurrency(total, currency)}</span>
                  <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
                </div>
              </div>
              
              {isExpanded && (
                <div className="account-details">
                  {/* Available Funds */}
                  {(account.funds_inr || account.funds_usd) && (
                    <div className="detail-section">
                      <h5>Available Funds</h5>
                      {account.funds_inr && (
                        <div className="detail-row">
                          <span className="detail-label">Cash (INR):</span>
                          <span className="detail-value cash">
                            {formatCurrency(currency === 'INR' ? account.funds_inr : convertCurrency(account.funds_inr, 'INR', 'USD'), currency)}
                          </span>
                        </div>
                      )}
                      {account.funds_usd && (
                        <div className="detail-row">
                          <span className="detail-label">Cash (USD):</span>
                          <span className="detail-value cash">
                            {formatCurrency(currency === 'USD' ? account.funds_usd : convertCurrency(account.funds_usd, 'USD', 'INR'), currency)}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {/* Stocks */}
                  {(account.stocks_inr || account.stocks_usd) && (
                    <div className="detail-section">
                      <h5>Stocks</h5>
                      {account.stocks_inr?.map((stock, index) => (
                        <div key={`inr-${index}`} className="detail-row">
                          <span className="detail-label">{stock.symbol || stock.name}:</span>
                          <span className="detail-value stock">
                            {formatCurrency(currency === 'INR' ? stock.current : convertCurrency(stock.current, 'INR', 'USD'), currency)}
                          </span>
                        </div>
                      ))}
                      {account.stocks_usd?.map((stock, index) => (
                        <div key={`usd-${index}`} className="detail-row">
                          <span className="detail-label">{stock.symbol || stock.name}:</span>
                          <span className="detail-value stock">
                            {formatCurrency(currency === 'USD' ? stock.current : convertCurrency(stock.current, 'USD', 'INR'), currency)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Mutual Funds */}
                  {account.mutual_funds && (
                    <div className="detail-section">
                      <h5>Mutual Funds</h5>
                      {account.mutual_funds.map((fund, index) => (
                        <div key={index} className="detail-row">
                          <span className="detail-label">{fund.name}:</span>
                          <span className="detail-value fund">
                            {formatCurrency(currency === 'INR' ? fund.current : convertCurrency(fund.current, 'INR', 'USD'), currency)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Cryptocurrencies */}
                  {(account.cryptos_inr || account.cryptos_usd) && (
                    <div className="detail-section">
                      <h5>Cryptocurrencies</h5>
                      {account.cryptos_inr?.map((crypto, index) => (
                        <div key={`inr-crypto-${index}`} className="detail-row">
                          <span className="detail-label">{crypto.name}:</span>
                          <span className="detail-value crypto">
                            {formatCurrency(currency === 'INR' ? crypto.current : convertCurrency(crypto.current, 'INR', 'USD'), currency)}
                          </span>
                        </div>
                      ))}
                      {account.cryptos_usd?.map((crypto, index) => (
                        <div key={`usd-crypto-${index}`} className="detail-row">
                          <span className="detail-label">{crypto.name}:</span>
                          <span className="detail-value crypto">
                            {formatCurrency(currency === 'USD' ? crypto.current : convertCurrency(crypto.current, 'USD', 'INR'), currency)}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  if (!portfolioData?.accounts) {
    return (
      <div className="account-details loading">
        <div className="loading-spinner"></div>
        <p>Loading account details...</p>
      </div>
    );
  }

  return (
    <div className="account-details">
      <div className="details-header">
        <h2>Account Details</h2>
        <p>Detailed breakdown of all your accounts and holdings</p>
      </div>
      
      {renderBankAccounts()}
      {renderBrokerAccounts()}
    </div>
  );
};

export default AccountDetails;

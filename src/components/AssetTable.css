/* Asset Table Styles */
.asset-table {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 24px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.table-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #f1f5f9;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.table-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-select {
  background: rgba(15, 23, 42, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: #f1f5f9;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-select:hover {
  border-color: rgba(148, 163, 184, 0.5);
  background: rgba(15, 23, 42, 0.7);
}

.filter-select:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

.asset-count {
  font-size: 0.875rem;
  color: #cbd5e1;
  font-weight: 500;
}

.table-container {
  overflow-x: auto;
  border-radius: 16px;
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.assets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.assets-table th {
  background: rgba(30, 41, 59, 0.5);
  color: #f1f5f9;
  font-weight: 700;
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 2px solid rgba(148, 163, 184, 0.2);
  position: sticky;
  top: 0;
  z-index: 1;
}

.assets-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.assets-table th.sortable:hover {
  background: rgba(30, 41, 59, 0.7);
  color: #60a5fa;
}

.assets-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  color: #cbd5e1;
  transition: all 0.3s ease;
}

.assets-table tbody tr {
  transition: all 0.3s ease;
}

.assets-table tbody tr:hover {
  background: rgba(148, 163, 184, 0.05);
}

.assets-table tbody tr.positive {
  border-left: 3px solid #34d399;
}

.assets-table tbody tr.negative {
  border-left: 3px solid #f87171;
}

.assets-table tbody tr.neutral {
  border-left: 3px solid #64748b;
}

.asset-name .name {
  display: block;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.25rem;
}

.asset-name .shares,
.asset-name .coins {
  display: block;
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

.type-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge.stock {
  background: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.type-badge.mutual-fund {
  background: rgba(34, 197, 94, 0.2);
  color: #86efac;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.type-badge.cryptocurrency {
  background: rgba(168, 85, 247, 0.2);
  color: #c4b5fd;
  border: 1px solid rgba(168, 85, 247, 0.3);
}

.broker {
  font-weight: 600;
  color: #e2e8f0;
}

.invested {
  color: #60a5fa;
  font-weight: 600;
}

.current {
  color: #34d399;
  font-weight: 600;
}

.gain-loss.positive {
  color: #34d399;
  font-weight: 700;
}

.gain-loss.negative {
  color: #f87171;
  font-weight: 700;
}

.percentage.positive {
  color: #34d399;
  font-weight: 700;
}

.percentage.negative {
  color: #f87171;
  font-weight: 700;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #94a3b8;
  font-style: italic;
}

/* Loading State */
.asset-table.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #f1f5f9;
}

.asset-table.loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.2);
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .asset-table {
    padding: 1.5rem;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
  }

  .table-header h2 {
    text-align: center;
    font-size: 1.3rem;
  }

  .table-controls {
    justify-content: space-between;
  }

  .assets-table {
    font-size: 0.8rem;
  }

  .assets-table th,
  .assets-table td {
    padding: 0.75rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .asset-table {
    padding: 1rem;
  }

  .table-header h2 {
    font-size: 1.2rem;
  }

  .assets-table {
    font-size: 0.75rem;
  }

  .assets-table th,
  .assets-table td {
    padding: 0.5rem 0.25rem;
  }

  .type-badge {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }
}
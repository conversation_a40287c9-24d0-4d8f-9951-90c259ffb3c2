import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { calculateRiskMetrics } from '../utils/riskMetrics';
import { formatPercentage } from '../services/currencyService';
import './RiskMetrics.css';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const RiskMetrics = ({ portfolioData, currency }) => {
  const [metrics, setMetrics] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (portfolioData) {
      const calculatedMetrics = calculateRiskMetrics(portfolioData, currency);
      setMetrics(calculatedMetrics);
    }
  }, [portfolioData, currency]);

  const getRiskLevel = (sharpeRatio) => {
    if (sharpeRatio > 1.5) return { level: 'Excellent', color: '#34d399' };
    if (sharpeRatio > 1.0) return { level: 'Good', color: '#60a5fa' };
    if (sharpeRatio > 0.5) return { level: 'Fair', color: '#fbbf24' };
    return { level: 'Poor', color: '#f87171' };
  };

  const getBetaInterpretation = (beta) => {
    if (beta > 1.2) return { text: 'High Risk', color: '#f87171' };
    if (beta > 0.8) return { text: 'Moderate Risk', color: '#fbbf24' };
    return { text: 'Low Risk', color: '#34d399' };
  };

  const generateReturnsChart = () => {
    if (!metrics) return null;

    const labels = metrics.portfolioReturns.map((_, index) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (metrics.portfolioReturns.length - 1 - index));
      return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
    });

    return {
      labels,
      datasets: [
        {
          label: 'Portfolio Returns',
          data: metrics.portfolioReturns.map(r => r * 100),
          borderColor: '#60a5fa',
          backgroundColor: 'rgba(96, 165, 250, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
        },
        {
          label: 'Benchmark Returns',
          data: metrics.benchmarkReturns.map(r => r * 100),
          borderColor: '#34d399',
          backgroundColor: 'rgba(52, 211, 153, 0.1)',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
        }
      ]
    };
  };

  const generateVolatilityChart = () => {
    if (!metrics) return null;

    const data = [
      { label: 'Portfolio', value: metrics.volatility * 100, color: '#60a5fa' },
      { label: 'Benchmark', value: Math.sqrt(metrics.benchmarkReturns.reduce((sum, r) => sum + r * r, 0) / metrics.benchmarkReturns.length) * Math.sqrt(12) * 100, color: '#34d399' },
    ];

    return {
      labels: data.map(d => d.label),
      datasets: [{
        label: 'Volatility (%)',
        data: data.map(d => d.value),
        backgroundColor: data.map(d => d.color),
        borderColor: data.map(d => d.color),
        borderWidth: 2,
      }]
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#f1f5f9',
          font: { size: 12, weight: 'bold' }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(30, 41, 59, 0.9)',
        titleColor: '#f1f5f9',
        bodyColor: '#cbd5e1',
        borderColor: 'rgba(148, 163, 184, 0.3)',
        borderWidth: 1,
      }
    },
    scales: {
      x: {
        grid: { color: 'rgba(148, 163, 184, 0.1)' },
        ticks: { color: '#cbd5e1', font: { size: 11 } }
      },
      y: {
        grid: { color: 'rgba(148, 163, 184, 0.1)' },
        ticks: { color: '#cbd5e1', font: { size: 11 } }
      }
    }
  };

  if (!metrics) {
    return (
      <div className="risk-metrics loading">
        <div className="loading-spinner"></div>
        <p>Calculating risk metrics...</p>
      </div>
    );
  }

  const riskLevel = getRiskLevel(metrics.sharpeRatio);
  const betaInterpretation = getBetaInterpretation(metrics.beta);

  return (
    <div className="risk-metrics">
      <div className="metrics-header">
        <h2>Risk & Performance Analytics</h2>
        <p>Advanced portfolio analysis with time-weighted returns and risk metrics</p>
      </div>

      <div className="metrics-tabs">
        <button
          className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab-btn ${activeTab === 'returns' ? 'active' : ''}`}
          onClick={() => setActiveTab('returns')}
        >
          Returns Analysis
        </button>
        <button
          className={`tab-btn ${activeTab === 'risk' ? 'active' : ''}`}
          onClick={() => setActiveTab('risk')}
        >
          Risk Analysis
        </button>
      </div>

      {activeTab === 'overview' && (
        <div className="metrics-overview">
          <div className="metrics-grid">
            <div className="metric-card primary">
              <div className="metric-header">
                <h3>XIRR</h3>
                <span className="metric-subtitle">Time-Weighted Return</span>
              </div>
              <div className="metric-value">
                <span className="main-value">{formatPercentage(metrics.xirr * 100).value}</span>
                <span className="metric-description">Annualized return considering timing of investments</span>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-header">
                <h3>Sharpe Ratio</h3>
                <span className="metric-subtitle">Risk-Adjusted Return</span>
              </div>
              <div className="metric-value">
                <span className="main-value" style={{ color: riskLevel.color }}>
                  {metrics.sharpeRatio.toFixed(2)}
                </span>
                <span className="metric-description">{riskLevel.level} risk-adjusted performance</span>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-header">
                <h3>Beta</h3>
                <span className="metric-subtitle">Market Sensitivity</span>
              </div>
              <div className="metric-value">
                <span className="main-value" style={{ color: betaInterpretation.color }}>
                  {metrics.beta.toFixed(2)}
                </span>
                <span className="metric-description">{betaInterpretation.text} vs benchmark</span>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-header">
                <h3>Volatility</h3>
                <span className="metric-subtitle">Annual Standard Deviation</span>
              </div>
              <div className="metric-value">
                <span className="main-value">{formatPercentage(metrics.volatility * 100).value}</span>
                <span className="metric-description">Portfolio price fluctuation</span>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-header">
                <h3>Max Drawdown</h3>
                <span className="metric-subtitle">Largest Peak-to-Trough Decline</span>
              </div>
              <div className="metric-value">
                <span className="main-value negative">{formatPercentage(metrics.maxDrawdown * 100).value}</span>
                <span className="metric-description">Worst historical loss period</span>
              </div>
            </div>

            <div className="metric-card">
              <div className="metric-header">
                <h3>VaR (95%)</h3>
                <span className="metric-subtitle">Value at Risk</span>
              </div>
              <div className="metric-value">
                <span className="main-value negative">{formatPercentage(metrics.var95 * 100).value}</span>
                <span className="metric-description">Maximum expected loss (95% confidence)</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'returns' && (
        <div className="returns-analysis">
          <div className="chart-container">
            <h3>Portfolio vs Benchmark Returns</h3>
            <div className="chart-wrapper">
              <Line data={generateReturnsChart()} options={chartOptions} />
            </div>
          </div>

          <div className="returns-summary">
            <div className="summary-item">
              <span className="label">Portfolio Annual Return:</span>
              <span className="value positive">{formatPercentage(metrics.annualReturn * 100).value}</span>
            </div>
            <div className="summary-item">
              <span className="label">Benchmark Annual Return:</span>
              <span className="value">{formatPercentage(metrics.benchmarkReturn * 100).value}</span>
            </div>
            <div className="summary-item">
              <span className="label">Excess Return (Alpha):</span>
              <span className={`value ${metrics.annualReturn > metrics.benchmarkReturn ? 'positive' : 'negative'}`}>
                {formatPercentage((metrics.annualReturn - metrics.benchmarkReturn) * 100).value}
              </span>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'risk' && (
        <div className="risk-analysis">
          <div className="chart-container">
            <h3>Volatility Comparison</h3>
            <div className="chart-wrapper">
              <Bar data={generateVolatilityChart()} options={chartOptions} />
            </div>
          </div>

          <div className="risk-insights">
            <div className="insight-card">
              <h4>Risk Assessment</h4>
              <p>Your portfolio has a beta of {metrics.beta.toFixed(2)}, indicating {betaInterpretation.text.toLowerCase()} relative to the market benchmark.</p>
            </div>
            <div className="insight-card">
              <h4>Volatility Analysis</h4>
              <p>Annual volatility of {formatPercentage(metrics.volatility * 100).value} suggests moderate price fluctuations compared to historical market patterns.</p>
            </div>
            <div className="insight-card">
              <h4>Risk-Adjusted Performance</h4>
              <p>Sharpe ratio of {metrics.sharpeRatio.toFixed(2)} indicates {riskLevel.level.toLowerCase()} risk-adjusted returns for the level of risk taken.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RiskMetrics;
